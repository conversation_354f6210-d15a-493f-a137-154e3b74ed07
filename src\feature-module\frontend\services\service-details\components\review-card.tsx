"use client"

import type React from "react"
import { useState } from "react"
import { Avatar, Button, Card, CardBody, CardHeader, Textarea, Input, Chip, Divider } from "@heroui/react"
import { Star, MessageCircle, Send, Image as ImageIcon, ThumbsUp, ThumbsDown, Heart, MoreHorizontal } from "lucide-react"
import type { Review, Reply } from "../service-review"
import ReviewImages from "../../../../components/ReviewImages/ReviewImages"
import { useAuth } from 'react-oidc-context'
import { toast } from 'react-toastify'

interface ReviewCardProps {
  review: Review
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
  onLike?: (reviewId: string, type: 'like' | 'dislike') => void
  onReplyLike?: (reviewId: string, replyId: string, type: 'like' | 'dislike') => void
}

// Component to display detailed ratings
const DetailedRatings = ({ review }: { review: Review }) => {
  const ratings = [
    { label: "Service Quality", value: review.serviceRating },
    { label: "Work Quality", value: review.qualityRating },
    { label: "Value for Money", value: review.valueRating },
    { label: "Communication", value: review.communicationRating },
    { label: "Timeliness", value: review.timelinessRating },
  ].filter(rating => rating.value !== undefined && rating.value !== null)

  if (ratings.length === 0) return null

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full">
          <Star className="w-4 h-4 text-blue-600 fill-blue-600" />
        </div>
        <h5 className="text-sm font-semibold text-gray-800">Detailed Ratings</h5>
      </div>
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {ratings.map((rating) => (
            <div key={rating.label} className="flex items-center justify-between bg-white rounded-lg p-3 shadow-sm border border-blue-100">
              <span className="text-sm font-medium text-gray-700">{rating.label}</span>
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[1, 2, 3, 4].map((star) => (
                    <Star
                      key={star}
                      className={`w-4 h-4 transition-colors ${
                        star <= (rating.value || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm font-semibold text-gray-700 bg-gray-100 px-2 py-1 rounded-full">
                  {rating.value}/4
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export function ReviewCard({ review, onReply, onLike, onReplyLike }: ReviewCardProps) {
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [replyContent, setReplyContent] = useState("")
  const [replyAuthor, setReplyAuthor] = useState("")
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set())
  const auth = useAuth()

  const handleReplySubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!replyContent.trim() || !replyAuthor.trim()) return

    onReply(review.id, {
      authorName: replyAuthor.trim(),
      content: replyContent.trim(),
      date: new Date().toISOString().split("T")[0],
      isBusinessOwner: false,
      replies: [],
      likes: 0,
      dislikes: 0,
      userReaction: null,
    })

    setReplyContent("")
    setReplyAuthor("")
    setShowReplyForm(false)
  }

  // Handle like/dislike for review
  const handleReviewLike = (type: 'like' | 'dislike') => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to react to reviews')
      return
    }
    if (onLike) {
      onLike(review.id, type)
    }
  }

  // Handle like/dislike for reply
  const handleReplyReaction = (replyId: string, type: 'like' | 'dislike') => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to react to replies')
      return
    }
    if (onReplyLike) {
      onReplyLike(review.id, replyId, type)
    }
  }

  // Toggle reply expansion
  const toggleReplyExpansion = (replyId: string) => {
    const newExpanded = new Set(expandedReplies)
    if (newExpanded.has(replyId)) {
      newExpanded.delete(replyId)
    } else {
      newExpanded.add(replyId)
    }
    setExpandedReplies(newExpanded)
  }

  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-gradient-to-br from-white to-gray-50">
      <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg border-b border-blue-100">
        <div className="flex items-start gap-4">
          <div className="relative">
            <Avatar
              src={review.customerAvatar || "/placeholder.svg"}
              name={review.customerName || 'Anonymous User'}
              size="lg"
              className="ring-2 ring-blue-200 ring-offset-2"
            />
            {review.isVerified && (
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 border-2 border-white rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">✓</span>
              </div>
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="font-bold text-lg text-gray-800">{review.customerName || 'Anonymous User'}</h3>
              <Chip size="sm" variant="flat" color="primary" className="text-xs">
                {review.date}
              </Chip>
            </div>
            {/* Show reviewer email if available */}
            {(review.reviewerInfo?.email || review.userEmail || review.email) && (
              <div className="mb-3 bg-white rounded-lg p-2 border border-blue-100">
                <span className="text-xs text-blue-600 font-medium">Contact: </span>
                <span className="text-sm text-gray-700 font-semibold">
                  {review.reviewerInfo?.email || review.userEmail || review.email}
                </span>
              </div>
            )}
            <div className="flex items-center gap-3 mb-3">
              <div className="flex bg-white rounded-full p-1 shadow-sm">
                {[1, 2, 3, 4].map((star) => (
                  <Star
                    key={star}
                    className={`w-5 h-5 transition-all duration-200 ${
                      star <= review.rating ? "fill-yellow-400 text-yellow-400 scale-110" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <Chip size="md" color="warning" variant="flat" className="font-bold">
                {review.rating}/4 Stars
              </Chip>
            </div>
            <div className="bg-white rounded-lg p-3 border border-blue-100 shadow-sm">
              <h4 className="font-bold text-gray-800 text-lg">{review.title}</h4>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardBody className="space-y-6 p-6">
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border-l-4 border-blue-400">
          <p className="text-gray-700 leading-relaxed text-base font-medium italic">
            "{review.content}"
          </p>
        </div>

        {/* Detailed Ratings */}
        <DetailedRatings review={review} />

        {/* Review Images - Enhanced Display */}
        {review.imageNames && review.imageNames.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full">
                <ImageIcon className="w-4 h-4 text-purple-600" />
              </div>
              <h5 className="text-sm font-semibold text-gray-800">Review Photos</h5>
              <Chip size="sm" variant="flat" color="secondary" className="text-xs">
                {review.imageNames.length} {review.imageNames.length === 1 ? 'photo' : 'photos'}
              </Chip>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
              <ReviewImages
                imageNames={review.imageNames}
                imageUrls={review.imageUrls}
                maxDisplay={4}
                size="lg"
                layout="grid-4"
                enablePreview={true}
                showCount={true}
                folderName="review-images"
              />
            </div>
          </div>
        )}

        {review.replies.length > 0 && (
          <div className="space-y-4">
            <Divider />
            <div className="space-y-4">
              {review.replies.map((reply) => (
                <div key={reply.id} className="flex gap-3 pl-4 border-l-2 border-gray-200">
                  <Avatar
                    src={reply.authorAvatar || "/placeholder.svg"}
                    name={reply.authorName}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{reply.authorName}</span>
                      {reply.isBusinessOwner && (
                        <Chip size="sm" color="primary" variant="flat" className="text-xs">
                          Business
                        </Chip>
                      )}
                      <span className="text-xs text-gray-500">{reply.date}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{reply.content}</p>

                    {/* Reply Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => handleReplyReaction(reply.id, 'like')}
                        startContent={
                          <ThumbsUp
                            className={`w-3 h-3 ${reply.userReaction === 'like' ? 'fill-blue-500 text-blue-500' : 'text-gray-400'}`}
                          />
                        }
                        className={`text-xs ${reply.userReaction === 'like' ? 'text-blue-500' : 'text-gray-400'}`}
                      >
                        {reply.likes}
                      </Button>
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => handleReplyReaction(reply.id, 'dislike')}
                        startContent={
                          <ThumbsDown
                            className={`w-3 h-3 ${reply.userReaction === 'dislike' ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
                          />
                        }
                        className={`text-xs ${reply.userReaction === 'dislike' ? 'text-red-500' : 'text-gray-400'}`}
                      >
                        {reply.dislikes}
                      </Button>

                      {/* Nested Reply Button */}
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => toggleReplyExpansion(reply.id)}
                        startContent={<MessageCircle className="w-3 h-3" />}
                        className="text-xs text-gray-400"
                      >
                        Reply
                      </Button>
                    </div>

                    {/* Nested Replies */}
                    {reply.replies && reply.replies.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {reply.replies.slice(0, expandedReplies.has(reply.id) ? reply.replies.length : 2).map((nestedReply) => (
                          <div key={nestedReply.id} className="flex gap-2 pl-2 border-l border-gray-100">
                            <Avatar
                              src={nestedReply.authorAvatar || "/placeholder.svg"}
                              name={nestedReply.authorName}
                              size="sm"
                              className="w-6 h-6"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-xs">{nestedReply.authorName}</span>
                                {nestedReply.isBusinessOwner && (
                                  <Chip size="sm" color="primary" variant="flat" className="text-[10px] px-1 py-0">
                                    Business
                                  </Chip>
                                )}
                                <span className="text-[10px] text-gray-500">{nestedReply.date}</span>
                              </div>
                              <p className="text-xs text-gray-600">{nestedReply.content}</p>
                            </div>
                          </div>
                        ))}

                        {reply.replies.length > 2 && !expandedReplies.has(reply.id) && (
                          <Button
                            variant="light"
                            size="sm"
                            onPress={() => toggleReplyExpansion(reply.id)}
                            className="text-xs text-blue-500"
                          >
                            Show {reply.replies.length - 2} more replies
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons - Enhanced */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {/* Like/Dislike Buttons */}
              <div className="flex items-center gap-2 bg-white rounded-full p-1 shadow-sm border border-gray-200">
                <Button
                  variant="light"
                  size="sm"
                  onPress={() => handleReviewLike('like')}
                  startContent={
                    <ThumbsUp
                      className={`w-4 h-4 transition-all ${review.userReaction === 'like' ? 'fill-blue-500 text-blue-500 scale-110' : 'text-gray-500 hover:text-blue-400'}`}
                    />
                  }
                  className={`transition-all ${review.userReaction === 'like' ? 'text-blue-500 bg-blue-50' : 'text-gray-500 hover:bg-blue-50'} rounded-full`}
                >
                  {review.likes}
                </Button>
                <div className="w-px h-6 bg-gray-300"></div>
                <Button
                  variant="light"
                  size="sm"
                  onPress={() => handleReviewLike('dislike')}
                  startContent={
                    <ThumbsDown
                      className={`w-4 h-4 transition-all ${review.userReaction === 'dislike' ? 'fill-red-500 text-red-500 scale-110' : 'text-gray-500 hover:text-red-400'}`}
                    />
                  }
                  className={`transition-all ${review.userReaction === 'dislike' ? 'text-red-500 bg-red-50' : 'text-gray-500 hover:bg-red-50'} rounded-full`}
                >
                  {review.dislikes}
                </Button>
              </div>

              {/* Reply Button */}
              <Button
                variant="flat"
                size="sm"
                color="primary"
                onPress={() => setShowReplyForm(!showReplyForm)}
                startContent={<MessageCircle className="w-4 h-4" />}
                className="bg-white shadow-sm border border-blue-200 hover:bg-blue-50 transition-all"
              >
                Reply ({review.replies.length})
              </Button>
            </div>

            {/* Review Metadata */}
            <div className="flex items-center gap-2">
              {review.isVerified && (
                <Chip size="sm" color="success" variant="solid" startContent="✓" className="shadow-sm">
                  Verified Review
                </Chip>
              )}
              {review.helpfulCount && review.helpfulCount > 0 && (
                <Chip size="sm" color="warning" variant="flat" className="bg-yellow-100 text-yellow-800 shadow-sm">
                  <Heart className="w-3 h-3 mr-1" />
                  {review.helpfulCount} helpful
                </Chip>
              )}
            </div>
          </div>
        </div>

        {showReplyForm && (
          <form onSubmit={handleReplySubmit} className="space-y-3 pt-2 border-t">
            <Input
              placeholder="Your name"
              value={replyAuthor}
              onValueChange={setReplyAuthor}
              isRequired
              variant="bordered"
            />
            <Textarea
              placeholder="Write your reply..."
              value={replyContent}
              onValueChange={setReplyContent}
              minRows={3}
              isRequired
              variant="bordered"
            />
            <div className="flex gap-2">
              <Button
                type="submit"
                size="sm"
                color="primary"
                startContent={<Send className="w-4 h-4" />}
              >
                Post Reply
              </Button>
              <Button
                type="button"
                variant="light"
                size="sm"
                onPress={() => setShowReplyForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </CardBody>
    </Card>
  )
}
